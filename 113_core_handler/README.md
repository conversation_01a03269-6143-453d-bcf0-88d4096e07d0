# Core Dump Trigger Program

这是一个可以触发不同信号生成coredump的程序，支持以下三种信号：

## 支持的信号

1. **SIGABRT** - 通过调用 `abort()` 函数触发
2. **SIGSEGV** - 通过解引用空指针触发段错误
3. **SIGUSR2** - 通过 `pthread_kill()` 在线程间发送信号触发

## 编译

```bash
make
```

或者手动编译：

```bash
g++ -std=c++11 -Wall -Wextra -g -pthread -o coredump_trigger src/main.cpp
```

## 使用方法

### 交互式运行

```bash
./coredump_trigger
```

程序会显示菜单，选择要触发的信号类型（1-3）。

### 非交互式运行

```bash
# 触发SIGABRT
echo "1" | ./coredump_trigger

# 触发SIGSEGV
echo "2" | ./coredump_trigger

# 触发SIGUSR2
echo "3" | ./coredump_trigger
```

## 设置coredump

程序会自动尝试设置coredump大小限制为无限制。如果需要手动设置：

```bash
ulimit -c unlimited
```

## 信号说明

### SIGABRT (信号6)
- 通过调用 `abort()` 函数触发
- 这是最常见的生成coredump的方式

### SIGSEGV (信号11)
- 通过解引用空指针触发段错误
- 模拟内存访问错误

### SIGUSR2 (信号12)
- 通过 `pthread_kill()` 从子线程发送给主线程
- 信号处理器接收到信号后调用 `abort()` 生成coredump
- 演示了线程间信号传递

## 清理

```bash
make clean
```

这会删除编译生成的可执行文件和可能的core文件。

## 注意事项

- 确保系统允许生成coredump文件
- 某些系统可能使用apport等工具处理crash，coredump文件可能位于 `/var/crash/` 目录
- 程序设计为在触发信号后立即终止并生成coredump
