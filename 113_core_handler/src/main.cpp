#include <iostream>
#include <signal.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/resource.h>
#include <cstring>
#include <cstdlib>

// 全局变量存储主线程ID
pthread_t main_thread_id;

// 信号处理函数
void signal_handler(int sig) {
    std::cout << "Received signal: " << sig << " (" << strsignal(sig) << ")" << std::endl;
    // 对于SIGUSR2，我们调用abort()来生成coredump
    if (sig == SIGUSR2) {
        std::cout << "SIGUSR2 received, calling abort() to generate coredump..." << std::endl;
        abort();
    }
}

// 线程函数，用于发送SIGUSR2信号
void* thread_function(void* arg) {
    sleep(1); // 等待1秒确保主线程准备好
    std::cout << "Thread sending SIGUSR2 to main thread..." << std::endl;

    // 使用pthread_kill发送SIGUSR2信号给主线程
    int result = pthread_kill(main_thread_id, SIGUSR2);
    if (result != 0) {
        std::cerr << "pthread_kill failed: " << strerror(result) << std::endl;
    }

    return nullptr;
}

// 触发SIGABRT
void trigger_sigabrt() {
    std::cout << "Triggering SIGABRT..." << std::endl;
    abort(); // 调用abort()函数触发SIGABRT
}

// 触发SIGSEGV
void trigger_sigsegv() {
    std::cout << "Triggering SIGSEGV..." << std::endl;
    int* null_ptr = nullptr;
    *null_ptr = 42; // 解引用空指针触发段错误
}

// 触发SIGUSR2（通过pthread_kill）
void trigger_sigusr2() {
    std::cout << "Triggering SIGUSR2 via pthread_kill..." << std::endl;

    // 创建线程来发送SIGUSR2信号
    pthread_t thread;
    int result = pthread_create(&thread, nullptr, thread_function, nullptr);
    if (result != 0) {
        std::cerr << "pthread_create failed: " << strerror(result) << std::endl;
        return;
    }

    // 等待信号
    std::cout << "Waiting for SIGUSR2 signal..." << std::endl;
    sleep(5); // 等待足够长的时间让线程发送信号

    // 如果程序还在运行，说明信号处理有问题
    std::cout << "Still running after SIGUSR2, something went wrong..." << std::endl;
}

// 设置coredump
void setup_coredump() {
    // 设置core dump文件大小限制为无限制
    struct rlimit rlim;
    rlim.rlim_cur = RLIM_INFINITY;
    rlim.rlim_max = RLIM_INFINITY;

    if (setrlimit(RLIMIT_CORE, &rlim) != 0) {
        perror("setrlimit failed");
        std::cerr << "Warning: Could not set core dump size limit" << std::endl;
    } else {
        std::cout << "Core dump size limit set to unlimited" << std::endl;
    }
}

int main() {
    // 保存主线程ID
    main_thread_id = pthread_self();

    // 设置coredump
    setup_coredump();

    // 只为SIGUSR2设置信号处理器，其他信号让系统默认处理以生成coredump
    signal(SIGUSR2, signal_handler);

    std::cout << "Core Dump Trigger Program" << std::endl;
    std::cout << "========================" << std::endl;
    std::cout << "Choose a signal to trigger:" << std::endl;
    std::cout << "1. SIGABRT (abort)" << std::endl;
    std::cout << "2. SIGSEGV (segmentation fault)" << std::endl;
    std::cout << "3. SIGUSR2 (via pthread_kill)" << std::endl;
    std::cout << "Enter your choice (1-3): ";

    int choice;
    std::cin >> choice;

    switch (choice) {
        case 1:
            trigger_sigabrt();
            break;
        case 2:
            trigger_sigsegv();
            break;
        case 3:
            trigger_sigusr2();
            break;
        default:
            std::cout << "Invalid choice. Defaulting to SIGSEGV..." << std::endl;
            trigger_sigsegv();
            break;
    }

    // 这行代码通常不会被执行到
    std::cout << "Program ended normally (this shouldn't happen)" << std::endl;
    return 0;
}